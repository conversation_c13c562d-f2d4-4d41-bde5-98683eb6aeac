const SkillHook = require('../../base/SkillHook');
const PlayerWrapper = require('../../classes/PlayerWrapper');
const GPlayerImp = require('../../classes/GPlayerImp');
const SkillStubBase = require('../../classes/SkillStubBase');
const Skill = require('../../classes/Skill');
const dataman = require('../../classes/Dataman');

// 技能218
class skill218 extends SkillHook {
    constructor() {
        super(218);
    }

    get Calculates() {
        return this.ReplaceCalculate(2, 2, [
            [
                function (skillPtr, originalFn) {
                    let skill = new Skill(skillPtr);
                    let player = new PlayerWrapper(skill.GetPlayer());
                    let stub = new SkillStubBase(218);

                    // 设置攻击范围：自身30米内最多50个目标
                    stub.setAOE(2, 30, 50);

                    // 附加真气上限150%的攻击力
                    let maxmp = player.GetMaxmp();
                    let plus = maxmp * 1.5;
                    skill.SetPlus(plus);

                    // 攻击9次
                    for (let i = 0; i < 9; i++) {
                        player.SetPerform(1);
                    }
                },
                1
            ]
        ]);
    }

    get GetTime() {
        return [
            // { 
            //     state: 1, 
            //     time: (skillPtr, originalFn) => {
            //         let skill = new Skill(skillPtr);
            //         let player = new PlayerWrapper(skill.GetPlayer());
            //         let gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            //         let baseTime = originalFn();
            //         return 500;
            //     }
            // },
            // { 
            //     state: 2, 
            //     time: (skillPtr, originalFn) => {
            //         let skill = new Skill(skillPtr);
            //         let player = new PlayerWrapper(skill.GetPlayer());
            //         let baseTime = originalFn();
            //         return 250 ;
            //     }


            // },
            { state: 1, time: 500 },
            { state: 2, time: 250 },
        ];
    }

    get CooldownTime() {
        return (skillPtr, originalFn) => {
            // 获取原始冷却时间
            const baseCD = originalFn();
            return baseCD;
        };
    }

    // 动态MP消耗
    get MpCost() {
        return (skillPtr, originalFn) => {
            let skill = new Skill(skillPtr);
            let player = new PlayerWrapper(skill.GetPlayer());
            let gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            const baseMp = 1;
            // 可以使用gplayerimp的方法
            const isGM = gplayerimp.CheckGMPrivilege();
            if (isGM) {
                return 0;  // GM不消耗MP
            }
            return baseMp;
        };
    }

    get Castdistance() {
        return (skillPtr, originalFn) => {
            let skill = new Skill(skillPtr);
            let player = new PlayerWrapper(skill.GetPlayer());
            let gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            return 26.0;
        };
    }

    get Praydistance() {
        return (skillPtr, originalFn) => {
            let skill = new Skill(skillPtr);
            let player = new PlayerWrapper(skill.GetPlayer());
            let gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            return 26.0;
        };
    }

    get Effectdistance() {
        return (skillPtr, originalFn) => {
            let skill = new Skill(skillPtr);
            let player = new PlayerWrapper(skill.GetPlayer());
            let gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            return 26.0;
        };
    }
}

class skill220 extends SkillHook {
    constructor() {
        super(220);
    }
    static baseNpcId = 16;
    get Calculates() {
        return this.ReplaceCalculate(2, 2, [
            [
                function (skillPtr, originalFn) {
                    let skill = new Skill(skillPtr);
                    let player = new PlayerWrapper(skill.GetPlayer());
                    let gplayerimp = GPlayerImp.fromPlayerWrapper(player);

                    originalFn(skillPtr);
                },
                1
            ]
        ]); 
    }

    get GetTime() {
        return [
            { state: 1, time: 500 },
            { state: 2, time: 250 },
        ]
    }

    get CooldownTime() {
        return (skillPtr, originalFn) => {
            // 获取原始冷却时间
            const baseCD = originalFn();
            return baseCD * 0.1;
        };
    }

    get MpCost() {
        return (skillPtr, originalFn) => {
            let skill = new Skill(skillPtr);
            let player = new PlayerWrapper(skill.GetPlayer());
            let gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            const baseMp = originalFn(skillPtr);
            return baseMp;
        };
    }
}

class skill221 extends SkillHook {
    constructor() {
        super(221);
    }

    static calulate221(skillPtr, originalFn) {
        let skill = new Skill(skillPtr);
        let player = new PlayerWrapper(skill.GetPlayer());
        let gplayerimp = GPlayerImp.fromPlayerWrapper(player);
        let stub = new SkillStubBase(221);

        // gplayerimp.取出物品(0, 7939, 10);
        // gplayerimp.统计物品(7939);
        // gplayerimp.getEquipList();
        // console.log(gplayerimp.统计物品(53576));
         originalFn(skillPtr);

    }

    get Calculates() {
        return this.ReplaceCalculate(2, 2, [
            [skill221.calulate221, 1]
        ]);
    }

    get GetTime() {
        return [
            { state: 1, time: 500 },
            { state: 2, time: 250 },
        ]
    }

    get StateAttack() {
        return (skillPtr, originalFn) => {
            let skill = new Skill(skillPtr);
            let player = new PlayerWrapper(skill.GetPlayer());

            originalFn();

            // 每次攻击有90%几率追加9段伤害，最多可累计追加90段伤害
            // 使用随机数判断90%概率
            if (Math.random() < 0.9) {
                // 追加9段伤害，每段伤害为真气上限的一定比例
                let maxmp = player.GetMaxmp();
                let additionalDamage = maxmp * 0.1; // 每段追加伤害为真气上限的10%

                // 设置追加攻击：9段伤害
                player.SetSecondattack(9, additionalDamage);
            }
        }
    }

    get CooldownTime() {
        return (skillPtr, originalFn) => {
            // 获取原始冷却时间
            const baseCD = originalFn(skillPtr);
            return baseCD * 0.1;
        };
    }
}


class skill516 extends SkillHook {
    constructor() {
        super(516);
    }

    get StateAttack() {
        return (skillPtr, originalFn) => {
            let skill = new Skill(skillPtr);
            let player = new PlayerWrapper(skill.GetPlayer());
            let gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            const effectamount = Math.random() * (20 - 9) + 9;
            player.SetEquipeffect(120.0, effectamount, 0, 10000, 10);
            player.SetGuilin(120.0, 1, 10000, 0.5, 23);
            player.SetHupo(120.0, 1, 10000, 0.5, 24);
            gplayerimp.creatureGenerator(88888, 36, 1);
            gplayerimp.summonNPC(16, 36, 1);
        };
    }

    get CooldownTime() {
        return (skillPtr, originalFn) => {
            const baseCD = originalFn(skillPtr);
            return baseCD;
        };
    }
}
//传送术
class skill389 extends SkillHook {
    constructor() {
        super(389);
    }

    static calculate2(skillPtr, originalFn) {
        let skill = new Skill(skillPtr);
        let player = new PlayerWrapper(skill.GetPlayer());
        let gplayerimp = GPlayerImp.fromPlayerWrapper(player);
        // gplayerimp.learnSchoolSkills();
        player.SetPerform(1);
    }

    get Calculates() {
        return this.ReplaceCalculate(2, 2, [
            [skill389.calculate2, 1]
        ]);
    }

    get GetTime() {
        return [
            { state: 1, time: 1000 },
        ]
    }

    get CooldownTime() {
        return (skillPtr, originalFn) => {
            const baseCD = originalFn(skillPtr);
            return baseCD;
        };
    }
}

class skill1740 extends SkillHook {
    constructor() {
        super(1740);
    }

    Calculate(skillPtr) {
        let skill = new Skill(skillPtr);
        let player = new PlayerWrapper(skill.GetPlayer());
        let gplayerimp = GPlayerImp.fromPlayerWrapper(player);
        // super.Calculate(player, skill, gplayerimp);
        player.SetIncskillaccu(120.0, 10.0, 1000, 1);
        for (let i = 0; i < 2; i++) {
            player.SetPerform(1);
        }
    }

    get StateAttack() {
        return (skillPtr, originalFn) => {
            let skill = new Skill(skillPtr);
            let player = new PlayerWrapper(skill.GetPlayer());
            let gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            originalFn(skillPtr);
            player.SetSecondattack(1, 17000);
        };
    }

    get CooldownTime() {
        return (skillPtr, originalFn) => {
            const baseCD = originalFn(skillPtr);
            return baseCD;
        };
    }

    get MpCost() {
        return (skillPtr, originalFn) => {
            const baseMp = originalFn(skillPtr);
            return baseMp;
        };
    }
}


class intanceSkill extends SkillHook {
    constructor(skillid) {
        super(skillid);
    }

    get GetTime() {
        return [
            { state: 1, time: 250 },
            { state: 2, time: 250 },
        ]
    }

    get CooldownTime() {
        return (skillPtr, originalFn) => {
            const baseCD = originalFn(skillPtr);
            return baseCD;
        };
    }
}

// 天罡丹鼎
class skill1506 extends intanceSkill {
    constructor() {
        super(1506);
    }
}

// 天罡贮元
class skill1507 extends intanceSkill {
    constructor() {
        super(1507);
    }
}

// 天罡战意
class skill1509 extends intanceSkill {
    constructor() {
        super(1509);
    }
}

// 天罡云体
class skill1510 extends intanceSkill {
    constructor() {
        super(1510);
    }
}

// 天罡步尘
class skill1511 extends intanceSkill {
    constructor() {
        super(1511);
    }
}

// 众志成城
class skill441 extends intanceSkill {
    constructor() {
        super(441);
    }
}

// 同生共死
class skill442 extends intanceSkill {
    constructor() {
        super(442);
    }
}

// 群策群力
class skill443 extends intanceSkill {
    constructor() {
        super(443);
    }
}
// 导出所有技能
module.exports = {
    activateSkills: function () {
        return {
            "重击": new skill218(),
            "勇气": new skill220(),
            "治疗": new skill221(),
            "群英荟萃": new skill516(),
            "传送术": new skill389(),
            "死亡一指": new skill1740(),
            "天罡丹鼎": new skill1506(),
            "天罡贮元": new skill1507(),
            "天罡战意": new skill1509(),
            "天罡云体": new skill1510(),
            "天罡步尘": new skill1511(),
            "众志成城": new skill441(),
            "同生共死": new skill442(),
            "群策群力": new skill443()
        };
    }
}; 
