// @ts-nocheck  // 忽略整个文件的类型检查

const CPlusClass = require("../base/CPlusClass");
const PlayerWrapper = require("./PlayerWrapper");
const SkillStubBase = require("./SkillStubBase");

const functionMap = new Map();

const getNativeFunction = (symbolOrAddress, ret, args) => {
  if (functionMap.has(symbolOrAddress)) {
    return functionMap.get(symbolOrAddress);
  }

  let address;
  if (symbolOrAddress.startsWith("0x")) {
    address = ptr(symbolOrAddress);
  } else {
    address = Module.getExportByName(null, symbolOrAddress);
  }

  const f = new NativeFunction(address, ret, args);
  functionMap.set(symbolOrAddress, f);
  return f;
};

class GPlayerImp extends CPlusClass {
  // 添加静态Map来存储玩家实例
  static allPlayerInstances = new Map();
  static currentPlayerInstance = null;

  static fromPlayerWrapper(playerWrapper) {
    const playerPtr = playerWrapper.getPointer();
    const gplayerImpPtr = playerPtr.add(0x4).readPointer();
    if (!gplayerImpPtr.isNull()) {
      return new GPlayerImp(gplayerImpPtr);
    }
    throw new Error("Could not get valid GPlayerImp pointer");
  }

  // 获取当前玩家实例的方法
  static getCurrentPlayer() {
    if (GPlayerImp.currentPlayerInstance) {
      return GPlayerImp.currentPlayerInstance;
    }

    try {
      // 简化方法：创建一个基本的实例用于测试
      // 我们不需要真正的玩家指针，只需要能够调用静态方法
      console.log('[获取玩家] 创建测试实例...');

      // 创建一个最小的实例，只用于调用静态方法
      const testInstance = Object.create(GPlayerImp.prototype);
      testInstance.pointer = ptr(0x1); // 最小的非空指针

      GPlayerImp.currentPlayerInstance = testInstance;
      return GPlayerImp.currentPlayerInstance;

    } catch (error) {
      console.log('[获取玩家] 获取当前玩家失败:', error);
      throw new Error("无法获取当前玩家实例");
    }
  }

  GetMoney() {
    return this.pointer.add(0x938).readInt();
  }

  GetPlayerLevel() {
    return this.pointer.add(0x290).readInt();
  }

  IsPlayerFemale() {
    return getNativeFunction("_ZN11gplayer_imp14IsPlayerFemaleEv", "int", ["pointer"])(this.pointer);
  }

  GetGender() {
    const isFemale = this.IsPlayerFemale();
    console.log(`[GetGender] 原始值: ${isFemale}, 位置: 0x4E`);
    return isFemale ? "女" : "男";
  }

  GetPlayerId() {
    return this.pointer.add(0x8).readPointer().add(0x40).readInt();
  }

  playerEquipItemListIndex() {
    return this.pointer.add(0x774);
  }

  IsCombatState() {
    return !!this.pointer.add(1670).readU8();
  }

  GetInventory() {
    return this.pointer.add(1880);
  }

  FilterMan() {
    return this.pointer.add(312);
  }

  //读取玩家身上所有的buff
  filterIds() {
    const mapPtr = this.FilterMan().add(288).toInt32();
    let begin = getNativeFunction('_ZN5abase15static_multimapIiP6filterNS_10fast_allocILi4ELi128EEEE5beginEv', 'pointer', ['int'])(mapPtr);
    let end = getNativeFunction('_ZN5abase15static_multimapIiP6filterNS_10fast_allocILi4ELi128EEEE3endEv', 'pointer', ['int'])(mapPtr);
    begin = parseInt(begin.toString());
    end = parseInt(end.toString());
    let filterArr = [];
    while (begin < end) {
      filterArr.push(ptr(begin).readInt());
      begin += 8;
    }
    return filterArr;
  }

  hasBuffId(buffId) {
    return this.filterIds().includes(buffId);
  }

  GetPlayerName() {
    const nameLength = this.pointer.add(4 * 993).readU32();
    const namePtr = this.pointer.add(3952);
    const name = namePtr.readUtf16String(nameLength / 2);
    return name || "未知玩家";
  }

  getUserID() {
    return this.pointer.add(1015).readInt();
  }

  GetTag() {
    return getNativeFunction("_ZN11gobject_imp12GetClientTagEv", "int", ["pointer"])(this.pointer);
  }

  GetCurTitle() {
    const getCurTitleFunc = new NativeFunction(ptr("0x0863B9BA"), "int16", [
      "pointer",
    ]);
    const titleId = getCurTitleFunc(this.pointer);
    if (titleId <= 0) {
      return "没有称号";
    }
    return titleId;
  }

  getMapId() {
    return getNativeFunction("_ZN16object_interface6GetTagEv", "int", [
      "pointer",
    ])(this.pointer);
  }

  ClrAllCoolDown() {
    return getNativeFunction("_ZN11gplayer_imp14ClrAllCoolDownEv", "int", ["pointer"])(
      this.pointer
    );
  }

  creatureGenerator(creatureId, duration, count) {
    this.ensurePointer();
    // 创建object_interface实例
    const oi = Memory.alloc(0x08);
    oi.add(0).writePointer(this.pointer); // 直接使用GPlayerImp的指针
    oi.add(4).writeU32(0);
    // 创建初始化minor_param结构
    const mp = Memory.alloc(0x48);
    // 设置参数
    mp.add(0).writeU32(creatureId);
    mp.add(0x08).writeU32(duration);
    mp.add(0x14).writeFloat(1.0);
    mp.add(0x18).writeFloat(1.0);
    mp.add(0x1c).writeFloat(1.0);

    // 调用CreateMinors函数
    const createMinorsFunc = new NativeFunction(
      ptr("0x08765D04"), // 使用与PlayerWrapper相同的地址
      "void",
      ["pointer", "pointer", "float"]
    );
    // 循环创建生物
    for (let i = 0; i < count; i++) {
      createMinorsFunc(oi, mp, 6.0); // 使用相同的浮点数参数
    }
  }

  summonNPC(npcId, duration, count) {
    this.ensurePointer();
    // 创建object_interface实例
    const oi = Memory.alloc(0x08);
    oi.add(0).writePointer(this.pointer);
    oi.add(4).writeU8(0);
    // 创建并初始化minor_param结构
    const mp = Memory.alloc(0x48);
    for (let i = 0; i < 0x48; i++) {
      mp.add(i).writeU8(0);
    }
    // 设置参数
    mp.add(0).writeInt(npcId);
    mp.add(0x08).writeInt(duration);
    // 调用CreateNPC函数
    const createNPCFunc = new NativeFunction(
      ptr("0x087643F0"), // object_interface::CreateNPC
      "void",
      ["pointer", "pointer", "float"]
    );
    // 循环创建指定数量的NPC
    for (let i = 0; i < count; i++) {
      createNPCFunc(oi, mp, 6.0);
    }
  }

  // 生成只有召唤者可以攻击的怪物
  myGenerator(id, count, lifetime, ownerType = 1) {
    id = parseInt(id);
    count = parseInt(count);
    lifetime = parseInt(lifetime);
    ownerType = parseInt(ownerType);
    const tag = this.GetTag();
    const posStruct = Memory.alloc(12);
    const parent = this.pointer.add(8).readPointer(); // _parent
    const playerPos = parent.add(0x30); // 玩家位置偏移
    const x = playerPos.readFloat();
    const y = playerPos.add(4).readFloat();
    const z = playerPos.add(8).readFloat();
    posStruct.writeFloat(x); // x
    posStruct.add(4).writeFloat(y); // y
    posStruct.add(8).writeFloat(z); // z 
    const objectInterface = Memory.alloc(8);
    objectInterface.writePointer(this.pointer);
    objectInterface.add(4).writeU32(0); // 初始化第二个字段为0   
    const createMonsterFunc = getNativeFunction("0x087640E6", "void", [// _ZN16object_interface13CreateMonsterEiiR9A3DVECTORiicf
      "pointer",  // object_interface* this
      "int32",    // monster ID
      "int32",    // map tag
      "pointer",  // A3DVECTOR* position
      "int32",    // lifetime 
      "int32",    // count
      "char",     // ownerType
      "float"     // radius (默认值6.0)
    ]);

    // 调用CreateMonster
    createMonsterFunc(
      objectInterface,  // this
      id,               // monster ID
      tag,              // map tag
      posStruct,        // position
      lifetime,         // lifetime
      count,            // count
      ownerType & 0xff, // ownerType (转换为char)
      6.0               // radius
    );

    return true;
  }

  SpendMoney(delta) {
    const currentMoney = this.GetMoney();
    if (currentMoney < delta) {
      return false;
    }
    // 直接写入新的金钱值
    const newMoney = currentMoney - delta;
    this.pointer.add(0x938).writeU32(newMoney);
    // 通知客户端金钱变化
    const runner = this.pointer.add(0x10).readPointer();
    const vtable = runner.readPointer();
    const spendMoneyFunc = new NativeFunction(
      vtable.add(0x1D4).readPointer(),
      "void",
      ["pointer", "uint32"]
    );
    spendMoneyFunc(runner, delta);
    return true;
  }

  CheckGMPrivilege() {
    return new NativeFunction(ptr('0x085D2ED8'), 'bool', ['pointer'])(this.pointer);
  }

  GetTargetType() {
    const oi = Memory.alloc(0x08);
    oi.add(0).writePointer(this.pointer);
    oi.add(4).writeU8(0);
    const xidPtr = new NativeFunction(ptr("0x087605BA"), "pointer", ["pointer"])(oi);
    return xidPtr.readU32();
  }

  GetTargetTemplateId() {
    const result = this.QueryObject(3, null, 0x48);
    if (!result.success) return null;
    const templateId = result.objectInfo.add(44).readInt();
    return templateId;
  }

  FullHPAndMP() {
    // 直接调用FullHPAndMP函数
    const fullHPAndMPFn = new NativeFunction(ptr('0x085FAA02'), 'void', ['pointer']);
    fullHPAndMPFn(this.pointer);
    console.log('[恢复] 已恢复满血满蓝');
    return true;
  }

  Changeshape(shape) {
    const changeshapeFn = new NativeFunction(ptr('0x085D2C0C'), 'void', ['pointer', 'int32']);
    changeshapeFn(this.pointer, shape);
    return true;
  }

  takeoutItem(inv_index, item_id, count) {
    const takeoutfuc = new NativeFunction(ptr('0x085CA6B2'), 'int', ['pointer', 'int', 'int', 'int']);
    return takeoutfuc(this.pointer, inv_index, item_id, count);
  }

  取出物品(inv_index, item_id, count) {
    const takeoutfuc = new NativeFunction(ptr('0x085CA6B2'), 'int', ['pointer', 'int', 'int', 'int']);
    return takeoutfuc(this.pointer, inv_index, item_id, count);
  }

  countitemById(itemId) {
    this.ensurePointer();
    // 获取玩家背包指针 (this + 0x758 是背包的偏移)
    const inventoryPtr = this.pointer.add(0x758);
    const countitembyidfuc = new NativeFunction(ptr('0x08634EF4'), 'int', ['pointer', 'int']);
    return countitembyidfuc(inventoryPtr, itemId);
  }

  统计物品(itemId) {
    this.ensurePointer();
    // 获取玩家背包指针 (this + 0x758 是背包的偏移)
    const inventoryPtr = this.pointer.add(0x758);
    const countitembyidfuc = new NativeFunction(ptr('0x08634EF4'), 'int', ['pointer', 'int']);
    return countitembyidfuc(inventoryPtr, itemId);
  }

  UseItemByID(where, itemId, num, useType = 0) {
    const useItemByIDFunc = new NativeFunction(ptr('0x085CADFE'), 'int32', ['pointer', 'pointer', 'int32', 'int32', 'uint32', 'int32']);
    // 使用相同的玩家指针作为pImp参数
    return useItemByIDFunc(this.pointer, this.pointer, where, itemId, num, useType
    );
  }

  getEquipIdByIndex(index) {
    const itemPtr = new NativeFunction(
      ptr('0x08634D28'),  // item_list::operator[]
      'pointer',
      ['pointer', 'uint32']
    )(this.playerEquipItemListIndex(), index);

    const itemId = itemPtr.readU32();
    // 检查无效ID
    if (itemId === 0 || itemId > 1000000) {
      return '空槽位';  // 返回空槽位值
    }
    return itemId;
  }

  getEquipList(size = 0x1E) {  // 默认大小30个槽位
    let equipList = [];
    const listPtr = this.playerEquipItemListIndex();

    // 获取装备列表大小
    const getSizeFunc = new NativeFunction(
      ptr('0x08633C6E'),  // item_list::Size
      'uint32',
      ['pointer']
    );
    const listSize = getSizeFunc(listPtr);

    // 使用传入的size和实际size中的较小值
    const actualSize = Math.min(size, listSize);

    console.log('========== 装备列表 ==========');
    // 获取每个装备的ID
    for (let i = 0; i < actualSize; i++) {
      const itemId = this.getEquipIdByIndex(i);
      equipList.push(itemId);
      console.log(`[${i}] ${itemId}`);
    }
    console.log('============================');
    return equipList;
  }

  // 查询对象信息 (可以查询玩家、NPC、地面物品)
  queryObject(xid, objectInfo = null) {
    this.ensurePointer();

    // 获取world指针 (通常在gplayer_imp的某个偏移位置)
    const worldPtr = this.pointer.add(0x4).readPointer(); // 假设world在偏移0x4

    // 创建XID结构 (8字节: type + id)
    const xidStruct = Memory.alloc(8);
    xidStruct.writeU32(xid.type || 3); // 默认查询物品
    xidStruct.add(4).writeU32(xid.id);

    // 创建object_info结构 (大小需要根据实际结构确定，这里假设64字节)
    const objInfo = objectInfo || Memory.alloc(64);

    // 调用world::QueryObject
    const queryObjectFunc = new NativeFunction(
      ptr('0x0859A7EE'),
      'int',
      ['pointer', 'pointer', 'pointer', 'bool']
    );

    const result = queryObjectFunc(worldPtr, xidStruct, objInfo, true);

    if (result) {
      // 解析object_info结构，返回有用信息
      return {
        success: true,
        position: {
          x: objInfo.readFloat(),
          y: objInfo.add(4).readFloat(),
          z: objInfo.add(8).readFloat()
        },
        // 可以根据需要添加更多字段
      };
    }

    return { success: false };
  }

  // 查询地面物品
  queryGroundItem(itemId) {
    return this.queryObject({ type: 3, id: itemId });
  }

  // 遍历附近的地面物品
  getNearbyGroundItems(radius = 50.0) {
    this.ensurePointer();
    const groundItems = [];

    try {
      // 获取world指针 (从gobject_imp._plane获取)
      const worldPtr = this.pointer.add(0x4).readPointer(); // _plane在偏移0x4
      if (!worldPtr || worldPtr.isNull()) {
        console.log('无法获取world指针');
        return groundItems;
      }

      // 获取玩家当前位置 (从gobject.pos获取，偏移0x30)
      const parent = this.pointer.add(8).readPointer(); // _parent在偏移0x8
      if (!parent || parent.isNull()) {
        console.log('无法获取parent指针');
        return groundItems;
      }

      const playerPos = parent.add(0x30); // gobject.pos在偏移0x30
      const playerX = playerPos.readFloat();
      const playerY = playerPos.add(4).readFloat();
      const playerZ = playerPos.add(8).readFloat();

      console.log(`玩家位置: (${playerX.toFixed(2)}, ${playerY.toFixed(2)}, ${playerZ.toFixed(2)})`);

      // 获取最大Matter数量
      const getMaxMatterCountFunc = new NativeFunction(
        ptr('0x0859FAA2'),
        'int',
        []
      );

      // 获取Matter by index的函数
      const getMatterByIndexFunc = new NativeFunction(
        ptr('0x085A073E'),
        'pointer',
        ['pointer', 'int']
      );

      let maxMatterCount;
      try {
        maxMatterCount = getMaxMatterCountFunc();
        console.log(`最大Matter数量: ${maxMatterCount}`);
      } catch (error) {
        console.log('无法获取最大Matter数量，使用默认值1000');
        maxMatterCount = 1000;
      }

      // 限制遍历数量，避免性能问题
      maxMatterCount = Math.min(maxMatterCount, 2000);

      for (let i = 0; i < maxMatterCount; i++) {
        try {
          const matterPtr = getMatterByIndexFunc(worldPtr, i);
          if (!matterPtr || matterPtr.isNull()) continue;

          // 检查Matter是否激活 (gobject.b_zombie应该为false)
          const bZombie = matterPtr.add(0xD).readU8(); // b_zombie在偏移0xD
          if (bZombie) continue; // 如果是zombie状态，跳过

          // 获取Matter位置 (gobject.pos在偏移0x30)
          const matterX = matterPtr.add(0x30).readFloat();
          const matterY = matterPtr.add(0x34).readFloat();
          const matterZ = matterPtr.add(0x38).readFloat();

          // 计算距离
          const dx = matterX - playerX;
          const dy = matterY - playerY;
          const dz = matterZ - playerZ;
          const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);

          if (distance <= radius) {
            // 获取Matter的XID (在偏移0x3C)
            const xidType = matterPtr.add(0x3C).readU32();
            const xidId = matterPtr.add(0x40).readU32();

            // 获取tag (在偏移0x24)
            const tag = matterPtr.add(0x24).readU32();

            groundItems.push({
              index: i,
              xid: { type: xidType, id: xidId },
              tag: tag,
              position: { x: matterX, y: matterY, z: matterZ },
              distance: distance
            });
          }
        } catch (error) {
          // 忽略单个Matter的错误，继续遍历
          continue;
        }
      }

      console.log(`找到${groundItems.length}个附近的地面物品`);
      return groundItems;

    } catch (error) {
      console.log('遍历地面物品时出错:', error);
      return groundItems;
    }
  }

  // 调试方法：检查各种指针和结构
  debugWorldStructure() {
    this.ensurePointer();

    try {
      console.log('=== 调试World结构 ===');

      // 检查gobject_imp结构
      console.log('GPlayerImp指针:', this.pointer);
      const planePtr = this.pointer.add(0x4).readPointer();
      console.log('_plane指针:', planePtr);
      const parentPtr = this.pointer.add(0x8).readPointer();
      console.log('_parent指针:', parentPtr);

      if (!planePtr || planePtr.isNull()) {
        console.log('_plane指针为空，尝试其他偏移');
        // 尝试其他可能的偏移
        for (let i = 0; i < 0x20; i += 4) {
          const testPtr = this.pointer.add(i).readPointer();
          if (testPtr && !testPtr.isNull()) {
            console.log(`偏移0x${i.toString(16)}: ${testPtr}`);
          }
        }
        return;
      }

      // 检查world结构
      console.log('=== World结构信息 ===');
      const worldPtr = planePtr;

      // 读取world的基本信息
      const wIndex = worldPtr.add(0x48).readU32();
      const wTag = worldPtr.add(0x4C).readU32();
      const playerCount = worldPtr.add(0xA94).readU32();

      console.log(`World - Index: ${wIndex}, Tag: ${wTag}, 玩家数量: ${playerCount}`);

      // 检查grid结构
      console.log('=== Grid结构信息 ===');
      const gridPtr = worldPtr; // grid在world开始位置

      const pTable = gridPtr.add(0x0).readPointer();
      const sliceCount = gridPtr.add(0x3C).readU32();
      const regRow = gridPtr.add(0x40).readU32();
      const regColumn = gridPtr.add(0x44).readU32();

      console.log(`Grid - pTable: ${pTable}, Slice数量: ${sliceCount}, 行: ${regRow}, 列: ${regColumn}`);

      if (pTable && !pTable.isNull()) {
        console.log('=== 检查前几个Slice ===');
        for (let i = 0; i < Math.min(5, sliceCount); i++) {
          const slicePtr = pTable.add(i * 0x28);
          const flag = slicePtr.add(0x4).readU32();
          const playerList = slicePtr.add(0x8).readPointer();
          const npcList = slicePtr.add(0xC).readPointer();
          const matterList = slicePtr.add(0x10).readPointer();

          console.log(`Slice[${i}] - Flag: ${flag}, Player: ${playerList}, NPC: ${npcList}, Matter: ${matterList}`);
        }
      }

    } catch (error) {
      console.log('调试时出错:', error);
    }
  }

  // 简单的ID2IDX转换（可能这个才是正确的TempID）
  static simpleId2Idx(id) {
    try {
      const id2idxFunc = new NativeFunction(ptr('0x0859A835'), 'int', ['int']);
      return id2idxFunc(id);
    } catch (error) {
      return -1;
    }
  }

  // 参考你的代码实现正确的QueryObject方法
  QueryObject(xidType, xidId, bufferSize = 0x38) {
    try {
      this.ensurePointer();
      const worldPtr = this.pointer.add(0x4).readPointer();
      if (!worldPtr || worldPtr.isNull()) {
        return { success: false };
      }

      // 创建XID结构
      const xidStruct = Memory.alloc(8);
      xidStruct.writeU32(xidType);
      xidStruct.add(4).writeU32(xidId);

      // 创建object_info结构
      const objectInfo = Memory.alloc(bufferSize);

      // 调用world::QueryObject
      const queryObjectFunc = new NativeFunction(
        ptr('0x0859A7EE'),
        'int',
        ['pointer', 'pointer', 'pointer', 'bool']
      );

      const result = queryObjectFunc(worldPtr, xidStruct, objectInfo, true);

      if (result) {
        return {
          success: true,
          objectInfo: objectInfo
        };
      }

      return { success: false };

    } catch (error) {
      return { success: false, error: error };
    }
  }

  // 获取目标的TemplateID（用于NPC/玩家）
  GetTargetTemplateId(xidType, xidId) {
    const result = this.QueryObject(xidType, xidId, 0x38);
    if (!result.success) return null;
    const templateId = result.objectInfo.add(44).readInt(); // 偏移44 = 0x2C
    return templateId;
  }

  //测试接口 出售版本应该删除
  InsertSkill(skillId, level = 1) {
    const oi = Memory.alloc(8);
    oi.writePointer(this.pointer);
    oi.add(4).writeInt(0);
    // 获取 SkillWrapper
    const skillWrapper = this.pointer.add(0xA4).readPointer();
    // 检查技能插件是否已注册
    const checkAddonFunc = new NativeFunction(ptr("0x08798AC6"), "pointer", []);  //addon_manager::GetInstance
    const addonManager = checkAddonFunc();
    // 创建技能学习插件数据
    const addonData = Memory.alloc(12); // addon_data 结构
    addonData.writeU32(0); // type
    addonData.add(4).writeU32(skillId); // skill_id
    addonData.add(8).writeU32(level); // skill_level
    // 创建技能学习插件
    const createAddonFunc = new NativeFunction(ptr("0x089B8ADA"), "int32", [
      "pointer",
      "pointer",
      "pointer",
      "pointer",
      "pointer",
    ]);
    // 调用插件的Activate函数
    const result = createAddonFunc(
      addonManager, // this
      addonData, // addon_data
      ptr(0), // item_body (null)
      this.pointer, // gactive_imp
      ptr(0) // parent item (null)
    );
    // 同步到客户端
    const runner = this.pointer.add(0x10).readPointer();
    const vtable = runner.readPointer();
    // 同步技能
    const syncSkillFunc = new NativeFunction(
      vtable.add(0x424).readPointer(),
      "void",
      ["pointer", "int32", "int32"]
    );
    syncSkillFunc(runner, skillId, level);
    // console.log(`[InsertSkill] 添加技能成功 ID:${skillId}, 等级:${level}`);
    return result;
  }

  InsertSkillPermament(skillId, level) {
    this.ensurePointer();
    // 创建 object_interface 实例
    const oi = Memory.alloc(8);
    oi.writePointer(this.pointer);
    oi.add(4).writeInt(0);
    // 获取 SkillWrapper 实例
    const skillWrapperPtr = this.pointer.add(0xA4);
    const skillWrapper = new SkillWrapper(skillWrapperPtr);
    // 调用 InsertSkillPermament
    const result = skillWrapper.InsertSkillPermament(skillId, level, oi);
    // 同步到客户端
    const runner = this.pointer.add(0x10).readPointer();
    if (!runner.isNull()) {
      const vtable = runner.readPointer();
      const syncSkillFunc = new NativeFunction(
        vtable.add(0x424).readPointer(),
        "void",
        ["pointer", "int32", "int32"]
      );
      syncSkillFunc(runner, skillId, level);
    }
    console.log(
      `[InsertSkillPermament] 永久添加技能${result === 0 ? "成功" : "失败"
      } ID:${skillId}, 等级:${level}`
    );
    return result === 0;
  }

  // 通过门派名直接加满该门派所有技能
  learnSkillByClass(schoolName, isPermanent = false) {
    // 先获取门派技能ID
    let skillIds = [];
    if (schoolName === '师徒技能') {
      skillIds = SkillStubBase.prototype.getSkillsByOccupation.call(SkillStubBase.prototype, 147);
    } else if (schoolName === '夫妻技能') {
      skillIds = SkillStubBase.prototype.getSkillsByOccupation.call(SkillStubBase.prototype, 152);
    } else if (schoolName === '通用技能') {
      skillIds = SkillStubBase.prototype.getSkillsByOccupation.call(SkillStubBase.prototype, 166);
    } else if (schoolName === '家族技能') {
      skillIds = SkillStubBase.prototype.getSkillsByOccupation.call(SkillStubBase.prototype, 131);
    } else {
      // 门派技能
      skillIds = SkillStubBase.prototype.printSchoolAllSkills.call(SkillStubBase.prototype, schoolName) || [];
      // 追加通用技能
      const masterSkills = SkillStubBase.prototype.getSkillsByOccupation.call(SkillStubBase.prototype, 147) || [];
      const coupleSkills = SkillStubBase.prototype.getSkillsByOccupation.call(SkillStubBase.prototype, 152) || [];
      const commonSkills = SkillStubBase.prototype.getSkillsByOccupation.call(SkillStubBase.prototype, 166) || [];
      const familySkills = SkillStubBase.prototype.getSkillsByOccupation.call(SkillStubBase.prototype, 131) || [];
      skillIds = [...new Set([...skillIds, ...masterSkills, ...coupleSkills, ...commonSkills, ...familySkills])];
    }
    if (!skillIds || skillIds.length === 0) {
      console.log(`[learnSkillByClass] 未找到门派/通用技能: ${schoolName}`);
      return false;
    }
    let count = 0;
    for (const skillId of skillIds) {
      const stub = new SkillStubBase(skillId);
      const maxLevel = stub.getMaxLearn();
      if (maxLevel > 0) {
        if (isPermanent) {
          this.InsertSkillPermament(skillId, maxLevel);
        } else {
          this.InsertSkill(skillId, maxLevel);
        }
        count++;
      }
    }
    console.log(`[learnSkillByClass] ${schoolName} 加满技能完成，共处理 ${count} 个技能！`);
    return true;
  }

  learnSchoolSkills() {
    let stub = new SkillStubBase();
    let skillIds = stub.getSkillsByClass(15);
    for (const skillId of skillIds) {
      const skillStub = new SkillStubBase(skillId);
      const maxLevel = skillStub.getMaxLearn();
      this.InsertSkill(skillId, maxLevel);
    }
  }


  // 修改各项声望  1青云贡献 2天音贡献   9是仙 10是魔 11是佛
  ModifyRegionReputation(idx, offset) {
    const modifyRegionReputationFunc = new NativeFunction(
      ptr('0x0863B15A'),
      'void', [
      'pointer', 'int32', 'int32']);
    modifyRegionReputationFunc(
      this.pointer,
      idx,
      offset);
  }

  SetCultivation(value) {   //1是仙 2是魔 3是仙魔 4是佛 5是仙佛 6是魔佛 7是仙魔佛
    // 调用原始的SetCultivation函数
    const setCultivationFunc = new NativeFunction(ptr('0x087824F8'), 'void', ['pointer', 'int32']);
    setCultivationFunc(this.pointer, value);
    const runner = this.pointer.add(0x10).readPointer();
    const vtable = runner.readPointer();
    const notifyFunc = new NativeFunction(vtable.add(0x518).readPointer(), 'void', ['pointer', 'int32']);
    notifyFunc(runner, value & 0xFF);
  }




  GetCurTargetID() {
    const oi = Memory.alloc(8);
    oi.writePointer(this.pointer);
    oi.add(4).writeInt(0);
    const getCurTargetIDFunc = new NativeFunction(
      ptr("0x087605BA"), // object_interface::GetCurTargetID
      "pointer",
      ["pointer"]
    );
    const xidPtr = getCurTargetIDFunc(oi);
    return xidPtr.add(4).readU32();
  }

  GetTargetTemplateId() {
    const result = this.QueryObject(2, null, 0x48);
    if (!result.success) return null;
    const templateId = result.objectInfo.add(44).readInt();
    return templateId;
  }


  QueryObject(type, targetId, requiredSize = 0x48) {
    targetId = this.GetCurTargetID();
    const oi = Memory.alloc(8);
    oi.writePointer(this.pointer);
    oi.add(4).writeInt(0);
    const xid = Memory.alloc(8);
    xid.writeInt(type); // type: 1=玩家, 2=NPC
    xid.add(4).writeInt(targetId);
    const objectInfo = Memory.alloc(requiredSize);
    const worldPtr = this.pointer.add(4).readPointer();
    const queryObjectFunc = new NativeFunction(
      ptr("0x0859A7EE"), //world::QueryObject
      "int32",
      ["pointer", "pointer", "pointer", "uint8"]
    );
    const result = queryObjectFunc(worldPtr, xid, objectInfo, 0);
    if (result > 0) {
      return {
        success: true,
        xid,
        objectInfo,
        id: xid.add(4).readInt(),
        type: xid.readInt(),
      };
    }
  }

  GetTargetPos() {
    const targetId = this.GetCurTargetID();
    const type = this.GetTargetType();
    let result;
    if (type === 1) {
      result = this.QueryObject(1, targetId, 0x48);
    } else {
      result = this.QueryObject(2, targetId, 0x48);
    }
    if (result && result.success) {
      const pos = {
        x: result.objectInfo.add(8).readFloat(),
        y: result.objectInfo.add(12).readFloat(),
        z: result.objectInfo.add(16).readFloat(),
      };

      return pos;
    }
    return null;
  }


  summonNPCFormation(
    baseNpcId,
    duration,
    count,
    formation = "circle",
    npcIds = null
  ) {
    this.ensurePointer();
    // 创建object_interface实例
    const oi = Memory.alloc(8);
    oi.writePointer(this.pointer);
    oi.add(4).writeInt(0);
    const parent = this.pointer.add(8).readPointer();
    const playerPos = parent.add(0x30);
    const targetPos = this.GetTargetPos();

    let spawnX, spawnY, spawnZ;
    if (targetPos && targetPos.z >= 0) {
      spawnX = targetPos.x;
      spawnY = targetPos.y;
      spawnZ = targetPos.z;
    } else {
      spawnX = playerPos.readFloat();
      spawnY = playerPos.add(4).readFloat();
      spawnZ = playerPos.add(8).readFloat();
    }
    const positions = this.calculateFormationPositions(
      formation,
      spawnX,
      spawnY,
      spawnZ,
      count
    );
    const createNPCFunc = new NativeFunction(
      ptr("0x087644AA"), // object_interface::CreateNPC
      "void",
      ["pointer", "pointer", "pointer"]
    );

    for (let i = 0; i < positions.length; i++) {
      const pos = Memory.alloc(12);
      pos.writeFloat(positions[i].x);
      pos.add(4).writeFloat(positions[i].y);
      pos.add(8).writeFloat(positions[i].z);

      const param = Memory.alloc(12);
      const npcId = npcIds ? npcIds[i % npcIds.length] : baseNpcId;
      param.writeInt(npcId);
      param.add(4).writeInt(0);
      param.add(8).writeInt(duration);

      const dx = positions[i].x - spawnX;
      const dz = positions[i].z - spawnZ;
      let angle = Math.atan2(dz, dx);

      let direction = Math.floor((angle / (2 * Math.PI)) * 256);
      direction = (direction + 128) % 256;
      if (direction < 0) direction += 256;
      param.add(4).writeU8(direction);
      createNPCFunc(oi, pos, param);
      Thread.sleep(0.001);
    }
    return true;
  }

  // 计算阵型位置
  calculateFormationPositions(formation, playerX, playerY, playerZ, npcCount) {
    const positions = [];
    const baseRadius = 15.0;
    switch (formation.toLowerCase()) {
      case "circle":
        // 圆形阵
        for (let i = 0; i < npcCount; i++) {
          const angle = (2 * Math.PI * i) / npcCount;
          const pos = {
            x: playerX + baseRadius * Math.cos(angle),
            y: playerY,
            z: playerZ + baseRadius * Math.sin(angle),
          };
          positions.push(pos);
        }
        break;

      case "callnpc":
        // 圆形阵参数调整
        const targetNpcCount = 20; // 目标NPC数量（原54）
        const compactRadius = baseRadius * 0.6; // 半径缩小为原来的60%（可调整）

        for (let i = 0; i < targetNpcCount; i++) {
          const angle = (2 * Math.PI * i) / targetNpcCount; // 角度间隔自动适应数量
          const pos = {
            x: playerX + compactRadius * Math.cos(angle),
            y: playerY,
            z: playerZ + compactRadius * Math.sin(angle),
          };
          positions.push(pos);
        }
        break;

      case "square":
        // 方形阵
        const side = Math.ceil(Math.sqrt(npcCount));
        const spacing = (baseRadius * 2) / side;
        for (let i = 0; i < npcCount; i++) {
          const row = Math.floor(i / side);
          const col = i % side;
          positions.push({
            x: playerX + spacing * (col - side / 2),
            y: playerY,
            z: playerZ + spacing * (row - side / 2),
          });
        }
        break;
      case "helix":
        // 螺旋上升阵型
        const heightRange = 50.0; // 上升高度
        for (let i = 0; i < npcCount; i++) {
          const angle = (6 * Math.PI * i) / npcCount;
          const height = (heightRange * i) / npcCount;
          positions.push({
            x: playerX + baseRadius * Math.cos(angle),
            y: playerY + height,
            z: playerZ + baseRadius * Math.sin(angle),
          });
        }
        break;
      case "staircase":
        // 阶梯上升阵型（更高版）
        const stepHeight = 1.0; // 每级台阶的高度（增加到5.0）
        const stepWidth = 5.0; // 每级台阶的水平间距
        const totalSteps = npcCount; // 台阶数等于NPC数量
        for (let i = 0; i < npcCount; i++) {
          const stepLevel = i; // 当前台阶级别
          positions.push({
            x: playerX + stepWidth * stepLevel,
            y: playerY + stepHeight * stepLevel,
            z: playerZ, // z轴保持不变，模拟直线楼梯
          });
        }
        break;
      case "sphere":
        // 球形阵型 - 位于玩家头顶
        const phi = Math.PI * (3 - Math.sqrt(5)); // 黄金角
        const heightOffset = 5.0; // 在玩家头顶上方的基础高度
        for (let i = 0; i < npcCount; i++) {
          const y = 1 - (i / (npcCount - 1)) * 2;
          const radius = Math.sqrt(1 - y * y);
          const theta = phi * i;
          positions.push({
            x: playerX + baseRadius * radius * Math.cos(theta),
            y: playerY + heightOffset + baseRadius * y, // 添加基础高度偏移
            z: playerZ + baseRadius * radius * Math.sin(theta),
          });
        }
        break;
      case "phoenix":
        // 凤凰阵型（展翅飞翔形状）
        const wingSpan = 80.0; // 翅膀跨度
        const bodyHeight = 50.0; // 身体高度
        const featherDensity = 0.2; // 羽毛密度
        const tailLength = 30.0; // 尾部长度
        for (let i = 0; i < npcCount; i++) {
          const t = i / (npcCount - 1); // 归一化参数
          const wingIdx = i % 2 === 0 ? 1 : -1; // 左右翅膀
          const angle = Math.PI * t * featherDensity; // 羽毛角度
          const isBody = t < 0.3; // 前 30% 为身体
          const x = isBody ? 0 : wingSpan * Math.sin(angle) * wingIdx * (1 - t);
          const y =
            bodyHeight * t +
            (isBody ? 0 : tailLength * Math.cos(angle) * (1 - t));
          positions.push({
            x: playerX + x,
            y: playerY + y,
            z:
              playerZ +
              (isBody ? 0 : tailLength * (1 - t) * Math.sin(t * Math.PI)),
          });
        }
        break;
      case "nebula":
        // 星云阵型（不规则扩散云团）
        const cloudRadius = 60.0; // 云团半径
        const densityFactor = 0.5; // 密度因子
        const chaosFactor = 0.4; // 混沌扰动
        for (let i = 0; i < npcCount; i++) {
          const t = i / (npcCount - 1); // 归一化参数
          const angle =
            ((4 * Math.PI * i) / npcCount) * (1 + Math.random() * chaosFactor); // 随机角度
          const radius =
            cloudRadius *
            Math.pow(t, densityFactor) *
            (1 + Math.random() * chaosFactor); // 指数分布半径
          const height = cloudRadius * (Math.random() - 0.5) * 2 * (1 - t); // 高度随机分布
          positions.push({
            x: playerX + radius * Math.cos(angle),
            y: playerY + height,
            z: playerZ + radius * Math.sin(angle),
          });
        }
        break;
      case "wave":
        // 波浪阵型
        const wave1Length = 2 * Math.PI;
        const amplitude = 5.0;
        for (let i = 0; i < npcCount; i++) {
          const x = (baseRadius * 2 * i) / npcCount - baseRadius;
          const phase = (i * wave1Length) / npcCount;
          positions.push({
            x: playerX + x,
            y: playerY + amplitude * Math.sin(phase),
            z: playerZ + amplitude * Math.cos(phase),
          });
        }
        break;
      case "dna":
        // DNA双螺旋阵型
        const dnaHeight = 50.0;
        const dnaRadius = baseRadius * 0.5;
        for (let i = 0; i < npcCount; i++) {
          const height = (dnaHeight * i) / npcCount;
          const angle = (6 * Math.PI * i) / npcCount;
          // 第一条螺旋
          if (i % 2 === 0) {
            positions.push({
              x: playerX + dnaRadius * Math.cos(angle),
              y: playerY + height,
              z: playerZ + dnaRadius * Math.sin(angle),
            });
          }
          // 第二条螺旋（相位差π）
          else {
            positions.push({
              x: playerX + dnaRadius * Math.cos(angle + Math.PI),
              y: playerY + height,
              z: playerZ + dnaRadius * Math.sin(angle + Math.PI),
            });
          }
        }
        break;
      case "heart":
        // 心形阵
        for (let i = 0; i < npcCount; i++) {
          const t = (2 * Math.PI * i) / npcCount;
          const x = 16 * Math.pow(Math.sin(t), 3);
          const z =
            13 * Math.cos(t) -
            5 * Math.cos(2 * t) -
            2 * Math.cos(3 * t) -
            Math.cos(4 * t);
          positions.push({
            x: playerX + baseRadius * 0.3 * x,
            y: playerY,
            z: playerZ + baseRadius * 0.3 * z,
          });
        }
        break;
      case "butterfly":
        // 蝴蝶形阵
        for (let i = 0; i < npcCount; i++) {
          const t = (2 * Math.PI * i) / npcCount;
          const x =
            Math.sin(t) *
            (Math.exp(Math.cos(t)) -
              2 * Math.cos(4 * t) -
              Math.pow(Math.sin(t / 12), 5));
          const z =
            Math.cos(t) *
            (Math.exp(Math.cos(t)) -
              2 * Math.cos(4 * t) -
              Math.pow(Math.sin(t / 12), 5));
          positions.push({
            x: playerX + baseRadius * 0.3 * x,
            y: playerY,
            z: playerZ + baseRadius * 0.3 * z,
          });
        }
        break;
      case "yin-yang":
        // 阴阳形阵
        for (let i = 0; i < npcCount; i++) {
          const t = (2 * Math.PI * i) / npcCount;
          let x, z;
          if (i < npcCount / 2) {
            // 阳鱼
            x = Math.cos(t) * (1 + Math.sin(t));
            z = Math.sin(t) * (1 + Math.sin(t));
          } else {
            // 阴鱼
            x = Math.cos(t) * (1 - Math.sin(t));
            z = Math.sin(t) * (1 - Math.sin(t));
          }
          positions.push({
            x: playerX + baseRadius * x,
            y: playerY,
            z: playerZ + baseRadius * z,
          });
        }
        break;
      case "galaxy":
        // 银河系阵型（多层旋转臂）
        const armCount = 4; // 螺旋臂数量
        const galaxyRadius = 50.0; // 最大半径
        const armThickness = 10.0; // 臂厚度
        const twistFactor = 0.3; // 扭曲因子
        const heightSpread = 20.0; // 高度分布范围
        for (let i = 0; i < npcCount; i++) {
          const t = i / (npcCount - 1); // 归一化参数
          const armIdx = i % armCount; // 螺旋臂索引
          const angle =
            2 * Math.PI * t +
            (armIdx * 2 * Math.PI) / armCount +
            twistFactor * Math.log(1 + t * galaxyRadius);
          const radius = galaxyRadius * t * (1 + Math.random() * 0.2); // 随机扰动半径
          positions.push({
            x: playerX + radius * Math.cos(angle),
            y:
              playerY +
              heightSpread * (Math.sin(angle) + (Math.random() - 0.5) * 0.5), // 高度波动
            z: playerZ + radius * Math.sin(angle),
          });
        }
        break;
      case "galaxy-spiral":
        // 星系螺旋阵
        const galaxyarms = 4; // 螺旋臂数量
        for (let i = 0; i < npcCount; i++) {
          const galaxyarm = i % arms;
          const r = baseRadius * (i / npcCount);
          const angle =
            (2 * Math.PI * i) / (npcCount / arms) +
            (arm * 2 * Math.PI) / arms +
            (r / baseRadius) * Math.PI;
          const spread = 0.3 * r; // 臂的展开程度
          positions.push({
            x: playerX + (r + Math.random() * spread) * Math.cos(angle),
            y: playerY + (Math.random() - 0.5) * spread,
            z: playerZ + (r + Math.random() * spread) * Math.sin(angle),
          });
        }
        break;
      case "spiral":
        // 原有的螺旋形阵保持不变
        const spiralturns = 2;
        for (let i = 0; i < npcCount; i++) {
          const angle = (spiralturns * 2 * Math.PI * i) / npcCount;
          const r = baseRadius * (i / npcCount);
          positions.push({
            x: playerX + r * Math.cos(angle),
            y: playerY,
            z: playerZ + r * Math.sin(angle),
          });
        }
        break;
      case "crystal":
        // 水晶阵型（多面棱柱结构）
        const crystalHeight = 70.0; // 总高度
        const facetCount = 6; // 棱面数量
        const facetRadius = 30.0; // 基础半径
        const jaggedFactor = 0.3; // 锯齿状扰动
        for (let i = 0; i < npcCount; i++) {
          const t = i / (npcCount - 1); // 归一化参数
          const facetIdx = i % facetCount; // 棱面索引
          const angle =
            (2 * Math.PI * facetIdx) / facetCount + t * Math.PI * jaggedFactor; // 棱面角度
          const radius =
            facetRadius *
            (1 - t * 0.6) *
            (1 + Math.cos(t * Math.PI * facetCount) * jaggedFactor); // 半径变化
          positions.push({
            x: playerX + radius * Math.cos(angle),
            y: playerY + crystalHeight * t,
            z: playerZ + radius * Math.sin(angle),
          });
        }
        break;
      case "cathedral":
        // 大教堂阵型（尖顶塔楼结构）
        const baseHeight = 80.0; // 基础高度
        const towerRadius = 30.0; // 塔底半径
        const spireCount = 8; // 尖顶数量
        const spireHeight = 40.0; // 尖顶高度
        for (let i = 0; i < npcCount; i++) {
          const t = i / (npcCount - 1); // 归一化参数
          const layer = Math.floor(t * spireCount); // 分层
          const angle =
            (2 * Math.PI * (i % Math.ceil(npcCount / spireCount))) /
            Math.ceil(npcCount / spireCount);
          const radius = towerRadius * (1 - t * 0.7); // 半径随高度缩小
          const height =
            baseHeight * t +
            (layer < spireCount ? spireHeight * Math.sin(angle) : 0); // 尖顶波动
          positions.push({
            x: playerX + radius * Math.cos(angle),
            y: playerY + height,
            z: playerZ + radius * Math.sin(angle),
          });
        }
        break;
      case "vortex":
        // 涡流阵型（旋转漏斗状）
        const vortexHeight = 100.0; // 总高度
        const funnelRadius = 40.0; // 底部半径
        const swirlFactor = 5.0; // 旋转圈数
        const pinchFactor = 0.2; // 顶部收缩因子
        for (let i = 0; i < npcCount; i++) {
          const t = i / (npcCount - 1); // 归一化参数
          const angle = swirlFactor * 2 * Math.PI * t; // 多圈旋转
          const radius =
            funnelRadius *
            (1 - t * (1 - pinchFactor)) *
            (1 + Math.sin(t * Math.PI) * 0.2); // 半径波动
          positions.push({
            x: playerX + radius * Math.cos(angle),
            y: playerY + vortexHeight * t,
            z: playerZ + radius * Math.sin(angle),
          });
        }
        break;
      case "tree":
        // 巨树阵型（分叉树状结构）
        const trunkHeight = 60.0; // 主干高度
        const branchRadius = 25.0; // 分支半径
        const branchLevels = 4; // 分支层数
        const branchAngle = Math.PI / 6; // 分支角度
        for (let i = 0; i < npcCount; i++) {
          const t = i / (npcCount - 1); // 归一化参数
          const level = Math.floor(t * branchLevels); // 分支层
          const branchIdx = i % Math.ceil(npcCount / branchLevels); // 分支索引
          const height = trunkHeight * t;
          const angle = branchIdx * branchAngle + (level * Math.PI) / 4; // 分支旋转角度
          const radius =
            level === 0
              ? 0
              : branchRadius * (1 - t / branchLevels) * Math.sin(t * Math.PI); // 分支半径
          positions.push({
            x: playerX + radius * Math.cos(angle),
            y: playerY + height,
            z: playerZ + radius * Math.sin(angle),
          });
        }
        break;
      case "pyramid":
        // 金字塔阵型
        const pyramidLayers = 6;
        let currentNpc = 0;
        for (
          let layer = 0;
          layer < pyramidLayers && currentNpc < npcCount;
          layer++
        ) {
          const layerSize = (pyramidLayers - layer) * 2; // 每层的边长
          const spacing = baseRadius / pyramidLayers; // NPC间距
          const height = layer * 5; // 层高

          for (let x = 0; x < layerSize && currentNpc < npcCount; x++) {
            for (let z = 0; z < layerSize && currentNpc < npcCount; z++) {
              // 只在金字塔边缘放置NPC
              if (
                x === 0 ||
                x === layerSize - 1 ||
                z === 0 ||
                z === layerSize - 1
              ) {
                positions.push({
                  x: playerX + (x - layerSize / 2) * spacing,
                  y: playerY + height,
                  z: playerZ + (z - layerSize / 2) * spacing,
                });
                currentNpc++;
              }
            }
          }
        }
        break;
      case "castle":
        // 城堡阵型
        const towerHeight = 30; // 塔高
        const towerCount = 4; // 塔数量
        const wallLength = baseRadius * 1.5; // 城墙长度

        // 添加四个角塔
        for (let tower = 0; tower < towerCount; tower++) {
          const angle = (tower * Math.PI) / 2;
          for (let height = 0; height < 6; height++) {
            positions.push({
              x: playerX + wallLength * Math.cos(angle),
              y: playerY + height * 5,
              z: playerZ + wallLength * Math.sin(angle),
            });
          }
        }
        // 添加城墙
        for (let wall = 0; wall < 4; wall++) {
          const startAngle = (wall * Math.PI) / 2;
          const endAngle = ((wall + 1) * Math.PI) / 2;
          for (let i = 1; i < 5; i++) {
            const t = i / 5;
            const angle = startAngle + (endAngle - startAngle) * t;
            positions.push({
              x: playerX + wallLength * Math.cos(angle),
              y: playerY + 10, // 城墙高度
              z: playerZ + wallLength * Math.sin(angle),
            });
          }
        }
        break;
      case "text":
        // 文字阵型
        const letters = {
          V: [
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [0, 1, 0, 1, 0],
            [0, 0, 1, 0, 0],
          ],
          5: [
            [1, 1, 1, 1, 1],
            [1, 0, 0, 0, 0],
            [1, 1, 1, 1, 1],
            [0, 0, 0, 0, 1],
            [1, 1, 1, 1, 1],
          ],
          8: [
            [1, 1, 1, 1, 1],
            [1, 0, 0, 0, 1],
            [1, 1, 1, 1, 1],
            [1, 0, 0, 0, 1],
            [1, 1, 1, 1, 1],
          ],
          7: [
            [1, 1, 1, 1, 1],
            [0, 0, 0, 0, 1],
            [0, 0, 0, 1, 0],
            [0, 0, 1, 0, 0],
            [0, 1, 0, 0, 0],
          ],
        };
        const text = "V587"; // 要显示的文字
        const letterSpacing = 7; // 字母间距，从7增加到12
        const dotSpacing = 4; // 点阵间距，从4增加到8
        let currentX = 0;
        const textheightOffset = 1; // 增加整体高度偏移

        for (let char of text) {
          if (char in letters) {
            const pattern = letters[char];
            for (let row = 0; row < pattern.length; row++) {
              for (let col = 0; col < pattern[row].length; col++) {
                if (pattern[row][col]) {
                  positions.push({
                    x: playerX + currentX + col * dotSpacing,
                    y:
                      playerY +
                      textheightOffset +
                      (pattern.length - row) * dotSpacing,
                    z: playerZ,
                  });
                }
              }
            }
            currentX += letterSpacing * dotSpacing;
          }
        }
        break;
      case "love":
        // 竖立 "LOVE" 阵型
        const loveScale = 6.0;
        const loveDepth = 2.0;
        const loveLetters = {
          L: [
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 0],
            [1, 1, 1, 1, 1],
          ],
          O: [
            [0, 1, 1, 1, 0],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [0, 1, 1, 1, 0],
          ],
          V: [
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [0, 1, 0, 1, 0],
            [0, 0, 1, 0, 0],
            [0, 0, 1, 0, 0],
          ],
          E: [
            [1, 1, 1, 1, 1],
            [1, 0, 0, 0, 0],
            [1, 1, 1, 1, 0],
            [1, 0, 0, 0, 0],
            [1, 1, 1, 1, 1],
          ],
        };
        const loveSequence = "LOVE";
        const loveWidth = 5.5;
        let loveNpcIndex = 0;

        for (
          let charIdx = 0;
          charIdx < loveSequence.length && loveNpcIndex < npcCount;
          charIdx++
        ) {
          const letter = loveLetters[loveSequence[charIdx]];
          if (!letter) continue;

          for (let row = 0; row < 5 && loveNpcIndex < npcCount; row++) {
            for (let col = 0; col < 5 && loveNpcIndex < npcCount; col++) {
              if (letter[row][col] === 1) {
                positions.push({
                  x: playerX + (col + charIdx * loveWidth) * loveScale,
                  y: playerY + (4 - row) * loveScale,
                  z: playerZ + loveDepth,
                });
                loveNpcIndex++;
              }
            }
          }
        }
        break;
      case "torus":
        // 环面（甜甜圈形）阵型
        const torusRadius = 30.0; // 主环半径
        const tubeRadius = 10.0; // 管半径
        const torusSegments = Math.ceil(npcCount / 4); // 主环上的分段数
        for (let i = 0; i < npcCount; i++) {
          const theta = (2 * Math.PI * (i % torusSegments)) / torusSegments; // 主环角度
          const phi = (2 * Math.PI * Math.floor(i / torusSegments)) / 4; // 管角度
          positions.push({
            x:
              playerX +
              (torusRadius + tubeRadius * Math.cos(phi)) * Math.cos(theta),
            y: playerY + tubeRadius * Math.sin(phi),
            z:
              playerZ +
              (torusRadius + tubeRadius * Math.cos(phi)) * Math.sin(theta),
          });
        }
        break;
      case "waveplus":
        // 波动阵型（正弦波分布）
        const waveAmplitude = 15.0; // 波幅
        const waveFrequency = 0.5; // 频率
        const waveLength = 40.0; // 波长
        for (let i = 0; i < npcCount; i++) {
          const t = i / (npcCount - 1); // 归一化参数
          const xOffset = waveLength * t - waveLength / 2; // X 轴分布
          positions.push({
            x: playerX + xOffset,
            y: playerY + waveAmplitude * Math.sin(waveFrequency * xOffset),
            z: playerZ + waveAmplitude * Math.cos(waveFrequency * xOffset),
          });
        }
        break;
      case "starburst":
        // 星爆阵型（向外辐射的星形）
        const starRadius = 25.0; // 基础半径
        const spikeCount = 5; // 尖刺数量
        const heightVariation = 10.0; // 高度变化
        for (let i = 0; i < npcCount; i++) {
          const angle = (2 * Math.PI * i) / npcCount;
          const radiusMod =
            i % spikeCount < spikeCount / 2 ? starRadius : starRadius * 1.5; // 交替半径
          positions.push({
            x: playerX + radiusMod * Math.cos(angle),
            y: playerY + heightVariation * Math.sin(angle * spikeCount), // 高度随角度变化
            z: playerZ + radiusMod * Math.sin(angle),
          });
        }
        break;
      case "cone":
        // 圆锥阵型（底部宽，顶部窄）
        const coneHeight = 60.0; // 圆锥高度
        const baseWidth = 20.0; // 底部半径
        const taperFactor = 0.1; // 顶部收缩因子
        for (let i = 0; i < npcCount; i++) {
          const t = i / (npcCount - 1); // 归一化高度参数
          const angle = (4 * Math.PI * i) / npcCount; // 多圈旋转
          const radius = baseWidth * (1 - t * (1 - taperFactor)); // 半径随高度缩小
          positions.push({
            x: playerX + radius * Math.cos(angle),
            y: playerY + coneHeight * t,
            z: playerZ + radius * Math.sin(angle),
          });
        }
        break;
      case "gridsphere":
        // 网格球形阵型（球面上的网格分布）
        const gridRadius = 35.0; // 球半径
        const latCount = Math.ceil(Math.sqrt(npcCount)); // 纬度分割数
        const lonCount = Math.ceil(npcCount / latCount); // 经度分割数
        for (let i = 0; i < npcCount; i++) {
          const latIdx = Math.floor(i / lonCount); // 纬度索引
          const lonIdx = i % lonCount; // 经度索引
          const phi = (Math.PI * latIdx) / latCount; // 纬度角度
          const theta = (2 * Math.PI * lonIdx) / lonCount; // 经度角度
          const radius = gridRadius * Math.sin(phi); // 水平半径
          positions.push({
            x: playerX + radius * Math.cos(theta),
            y: playerY + gridRadius * Math.cos(phi),
            z: playerZ + radius * Math.sin(theta),
          });
        }
        break;
      case "doublehelix":
        // 双螺旋阵型（两条交错的螺旋）
        const helixRadius = 20.0; // 螺旋半径
        const helixHeight = 80.0; // 总高度
        const turns = 3; // 螺旋圈数
        for (let i = 0; i < npcCount; i++) {
          const t = i / (npcCount - 1); // 归一化参数
          const angle = turns * 2 * Math.PI * t; // 旋转角度
          const strand = i % 2 === 0 ? 1 : -1; // 交错的双螺旋
          positions.push({
            x: playerX + helixRadius * Math.cos(angle) * strand,
            y: playerY + helixHeight * t,
            z: playerZ + helixRadius * Math.sin(angle),
          });
        }
        break;
      default:
        // 默认使用圆形阵
        return this.calculateFormationPositions(
          "circle",
          playerX,
          playerY,
          playerZ,
          npcCount
        );
    }
    return positions;
  }

}

module.exports = GPlayerImp;
